/* pages/siteStatus/siteStatus.wxss */
.container {
  padding: 24rpx;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 刷新时间显示 */
.refresh-time {
  text-align: center;
  padding: 16rpx 0;
  margin-bottom: 16rpx;
}

.refresh-text {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 区域数据 */
.area-section {
  margin-bottom: 24rpx;
}

.area-container {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.05);
}

.area-title {
  padding: 20rpx 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #f9f9f9;
}

.model-list {
  display: flex;
  flex-wrap: wrap;
  padding: 16rpx 12rpx;
}

.model-item {
  width: 50%;
  padding: 12rpx;
  box-sizing: border-box;
}

/* 长名称数据项占满整行 */
.model-item-full {
  width: 100%;
}

.model-name {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 长名称允许自动换行 */
.model-name-long {
  white-space: normal;
  word-break: break-all;
  line-height: 1.4;
}

.model-value {
  display: flex;
  align-items: baseline;
}

.value-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 告警状态样式 */
.value-alarm {
  color: #fa5151;
}

.value-unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

/* 自动刷新提示 */
.auto-refresh-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  color: #888;
  font-size: 24rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin: 32rpx 0;
}

.refresh-time {
  margin-top: 8rpx;
  color: #999;
  font-size: 22rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120rpx;
}

/* 底部留白 */
.footer-space {
  height: 80rpx;
} 