<!--pages/siteStatus/siteStatus.wxml-->
<custom-navbar title="{{deviceInfo.deviceName || '站点运行状态'}}"
  show-back="{{true}}"
  home-btn="{{true}}"
  title-align="center"
  font-size="16px"
  font-color="#333"
  bg-color="#fff"></custom-navbar>
<global-message></global-message><!-- 消息通知 -->
<view class="container">
  <!-- 刷新时间显示 -->
  <view wx:if="{{lastRefreshTime}}" class="refresh-time">
    <text class="refresh-text">最后更新：{{lastRefreshTime}}</text>
  </view>

  <!-- 运行数据区域分类 -->
  <view class="area-section">
    <!-- 各个区域数据 -->
    <block wx:for="{{areaTypes}}" wx:key="index">
      <view wx:if="{{thingsModelMap[item.key] && thingsModelMap[item.key].length > 0}}" class="area-container">
        <view class="area-title">{{item.name}}</view>
        <view class="model-list">
          <view class="model-item {{model.isLongName ? 'model-item-full' : ''}}" wx:for="{{thingsModelMap[item.key]}}" wx:for-item="model" wx:key="id">
            <view class="model-name {{model.isLongName ? 'model-name-long' : ''}}">{{model.name}}</view>
            <view class="model-value">
              <text class="value-text {{(model.displayValue === '异常' || model.displayValue === '报警' || model.displayValue === '告警') ? 'value-alarm' : ''}}">{{model.displayValue || model.value || '--'}}</text>
              <text class="value-unit">{{model.unit || ''}}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>


  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-state">
    <t-loading theme="circular" size="40rpx" text="数据加载中..." />
  </view>

  <!-- 底部留白 -->
  <view class="footer-space"></view>
</view> 