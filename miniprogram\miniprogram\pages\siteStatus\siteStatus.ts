// pages/siteStatus/siteStatus.ts
import {http} from "../../utils/request";
import { formatTime } from '../../utils/util';

// 获取应用实例
const APP = getApp<IAppOption>();

Page({
  data: {
    deviceId: 0,
    loading: true,
    deviceInfo: { deviceName: '' } as any, // 只保留设备名称
    thingsModelMap: {} as any, // 各区域物模型数据
    areaTypes: [
      { key: 'L', name: '低区', alternateKey: 'dq' },
      { key: 'M', name: '中区', alternateKey: 'zq' },
      { key: 'H', name: '高区', alternateKey: 'gq' },
      { key: 'S', name: '超高区' },
      { key: 'BL', name: '商业低区'},
      { key: 'O', name: '其他区' },
      { key: 'other', name: '未分类' }
    ],
    isRequesting: false, // 是否正在请求数据，避免重复请求
    longNameThreshold: 8, // 设置名称长度阈值，超过此长度视为长名称
    autoRefreshTimer: null as any, // 自动刷新定时器
    lastRefreshTime: '' // 上次刷新时间
  },

  onLoad(options: any) {
    if (options.deviceId) {
      this.setData({
        deviceId: Number(options.deviceId)
      });
      this.fetchDeviceStatus();
      // 启动自动刷新
      this.startAutoRefresh();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onUnload() {
    // 页面卸载时清除定时器
    this.clearAutoRefresh();
  },

  onHide() {
    // 页面隐藏时清除定时器
    this.clearAutoRefresh();
  },

  onShow() {
    // 页面显示时重新启动定时器
    this.startAutoRefresh();
  },

  // 启动自动刷新
  startAutoRefresh() {
    // 先清除可能存在的定时器
    this.clearAutoRefresh();
    
    // 设置新的定时器，每10秒刷新一次
    const timer = setInterval(() => {
      this.fetchDeviceStatus();
    }, 10000); // 10秒
    
    this.setData({
      autoRefreshTimer: timer
    });
  },

  // 清除自动刷新定时器
  clearAutoRefresh() {
    if (this.data.autoRefreshTimer) {
      clearInterval(this.data.autoRefreshTimer);
      this.setData({
        autoRefreshTimer: null
      });
    }
  },

  // 获取设备状态数据
  fetchDeviceStatus() {
    // 避免重复请求
    if (this.data.isRequesting) {
      return;
    }
    
    const { deviceId } = this.data;
    this.setData({ 
      loading: true,
      isRequesting: true
    });
    
    // 使用DeviceController中的runningStatusMap接口获取设备运行状态数据
    http.get('/iot/device/runningStatusMap', {
      deviceId
    }).then((result: any) => {
      console.log("设备状态数据:", result.data);
      
      // 简化的设备基本信息获取 - 只获取设备名称
      this.fetchDeviceName();
      
      // 处理物模型数据 - 兼容数据结构
      let thingsModelMap = result.data || {};

      // 处理数据结构，确保数据可用
      this.processThingsModelData(thingsModelMap);

      // 过滤空白数据
      this.filterEmptyData(thingsModelMap);

      // 处理布尔值显示
      this.processBooleanValues(thingsModelMap);

      // 检测长名称并设置标记
      this.detectLongNames(thingsModelMap);
      
      // 获取当前时间作为刷新时间
      const now = new Date();
      const formattedTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      
      this.setData({
        thingsModelMap: thingsModelMap,
        loading: false,
        isRequesting: false,
        lastRefreshTime: formattedTime
      });
    }).catch((error: any) => {
      console.error("获取设备状态失败:", error);
      this.setData({ 
        loading: false,
        isRequesting: false
      });
      wx.showToast({
        title: '获取设备状态失败',
        icon: 'error'
      });
    });
  },

  // 处理物模型数据，确保新旧数据结构都能正常显示
  processThingsModelData(thingsModelMap: any) {
    // 确保每个区域都有数据，即使是空数组
    this.data.areaTypes.forEach(areaType => {
      if (!Array.isArray(thingsModelMap[areaType.key])) {
        thingsModelMap[areaType.key] = [];
      }

      // 处理可能的替代键（如 gq、zq、dq）
      if (areaType.alternateKey && Array.isArray(thingsModelMap[areaType.alternateKey]) && thingsModelMap[areaType.alternateKey].length > 0) {
        // 如果主键是空的但替代键有数据，则使用替代键的数据
        if (thingsModelMap[areaType.key].length === 0) {
          thingsModelMap[areaType.key] = thingsModelMap[areaType.alternateKey];
        }
      }
    });

    return thingsModelMap;
  },

  // 过滤空白数据，移除value为空字符串的数据项
  filterEmptyData(thingsModelMap: any) {
    // 遍历所有区域的数据
    Object.keys(thingsModelMap).forEach(key => {
      if (Array.isArray(thingsModelMap[key])) {
        // 过滤掉value为空字符串、null、undefined或只包含空白字符的数据项
        thingsModelMap[key] = thingsModelMap[key].filter((item: any) => {
          // 检查value是否为有效值
          if (item.value === null || item.value === undefined) {
            return false;
          }

          // 如果是字符串，检查是否为空或只包含空白字符
          if (typeof item.value === 'string') {
            return item.value.trim() !== '';
          }

          // 如果是数字0，保留
          if (typeof item.value === 'number') {
            return true;
          }

          // 如果是布尔值，保留
          if (typeof item.value === 'boolean') {
            return true;
          }

          // 其他情况保留
          return true;
        });
      }
    });

    return thingsModelMap;
  },

  // 检测长名称并设置标记
  detectLongNames(thingsModelMap: any) {
    const { longNameThreshold } = this.data;
    
    // 遍历所有区域的数据
    Object.keys(thingsModelMap).forEach(key => {
      if (Array.isArray(thingsModelMap[key])) {
        thingsModelMap[key].forEach((item: any) => {
          // 检测名称长度，如果超过阈值，标记为长名称
          if (item.name && item.name.length > longNameThreshold) {
            item.isLongName = true;
          } else {
            item.isLongName = false;
          }
        });
      }
    });
    
    return thingsModelMap;
  },

  // 处理布尔值，转换为中文显示
  processBooleanValues(thingsModelMap: any) {
    // 遍历所有区域的数据
    Object.keys(thingsModelMap).forEach(key => {
      if (Array.isArray(thingsModelMap[key])) {
        thingsModelMap[key].forEach((item: any) => {
          // 如果值是布尔值字符串，则转换为中文显示
          if (item.value === 'true' || item.value === 'false') {
            item.displayValue = this.getBoolDisplayText(item);
          } else {
            item.displayValue = item.value;
          }
        });
      }
    });
    
    return thingsModelMap;
  },

  // 判断布尔值是否为true
  isBoolValueTrue(value: any): boolean {
    return value === true || value === 'true' || value === '1' || value === 1;
  },

  // 获取布尔值的显示文本
  getBoolDisplayText(item: any) {
    // 首先检查值是否为布尔类型或布尔字符串
    const isTrue = this.isBoolValueTrue(item.value);
    
    // 判断设备类型
    const isFault = item.name.includes('故障');
    const isAlarm = item.name.includes('告警') || item.name.includes('报警') || 
                   (item.id && (item.id.includes('Alarm') || item.id.includes('alarm')));
    const isValve = item.name.includes('阀门') || 
                   (item.id && (item.id.includes('Valve') || item.id.includes('valve')));
    const isStatus = item.name.includes('状态') || item.name.includes('运行') || 
                    (item.id && (item.id.includes('Status') || item.id.includes('Run')));
    const isSwitch = item.name.includes('开关') || 
                    (item.id && (item.id.includes('Switch')));
    
    if (isFault || isAlarm) {
      // 故障和告警类型的布尔值
      return isTrue ? '异常' : '正常';
    } else if (isValve) {
      // 阀门类型显示开/关
      return isTrue ? '开' : '关';
    } else if (isStatus) {
      // 状态类型显示运行/停止
      return isTrue ? '运行' : '停止';
    } else if (isSwitch) {
      // 开关类型显示开/关
      return isTrue ? '开' : '关';
    } else {
      // 其他类型的布尔值，使用是/否
      return isTrue ? '是' : '否';
    }
  },

  // 获取设备名称 - 简化的API调用
  fetchDeviceName() {
    const { deviceId } = this.data;
    
    http.get('/iot/device/' + deviceId, {}).then((result: any) => {
      if (result.data && result.data.deviceName) {
        this.setData({
          'deviceInfo.deviceName': result.data.deviceName,
          'deviceInfo.serialNumber': result.data.serialNumber
        });
      }
    }).catch((error: any) => {
      console.error("获取设备名称失败:", error);
    });
  },

  onShareAppMessage() {
    const { deviceId } = this.data;
    const deviceName = this.data.deviceInfo.deviceName || '站点';
    
    return {
      title: `${deviceName}运行状态`,
      path: `/pages/siteStatus/siteStatus?deviceId=${deviceId}`
    };
  }
}); 